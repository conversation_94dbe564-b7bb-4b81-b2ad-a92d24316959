import React from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";

// Sample data for school admin activity - this would come from your dashboard API
const generateSchoolActivityData = () => {
  const data = [];
  const currentDate = new Date();
  
  for (let i = 89; i >= 0; i--) {
    const date = new Date(currentDate);
    date.setDate(date.getDate() - i);
    
    // Generate sample data - replace with real API data
    const students = Math.floor(Math.random() * 20) + 80;
    const teachers = Math.floor(Math.random() * 5) + 10;
    
    data.push({
      date: date.toISOString().split('T')[0],
      students: students,
      teachers: teachers,
    });
  }
  
  return data;
};

const schoolChartConfig = {
  students: {
    label: "Students",
    color: "hsl(var(--primary))",
  },
  teachers: {
    label: "Teachers",
    color: "hsl(var(--muted-foreground))",
  },
};

export function SchoolAdminActivityChart({ isLoading = false }) {
  const chartData = generateSchoolActivityData();

  return (
    <ChartAreaInteractive
      data={chartData}
      config={schoolChartConfig}
      title="School Growth"
      description="Students and teachers enrollment over time"
      primaryKey="students"
      secondaryKey="teachers"
      dateKey="date"
      height="300px"
      showTimeRange={true}
      defaultTimeRange="90d"
      isLoading={isLoading}
    />
  );
}
