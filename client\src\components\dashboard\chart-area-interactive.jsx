import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxi<PERSON> } from "recharts";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

export const description = "A reusable interactive area chart component";

// Default sample data for demonstration
const defaultChartData = [
  { date: "2024-04-13", primary: 342, secondary: 380 },
  { date: "2024-04-14", primary: 137, secondary: 220 },
  { date: "2024-04-15", primary: 120, secondary: 170 },
  { date: "2024-04-16", primary: 138, secondary: 190 },
  { date: "2024-04-17", primary: 446, secondary: 360 },
  { date: "2024-04-18", primary: 364, secondary: 410 },
  { date: "2024-04-19", primary: 243, secondary: 180 },
  { date: "2024-04-20", primary: 89, secondary: 150 },
  { date: "2024-04-21", primary: 137, secondary: 200 },
  { date: "2024-04-22", primary: 224, secondary: 170 },
  { date: "2024-04-23", primary: 138, secondary: 230 },
  { date: "2024-04-24", primary: 387, secondary: 290 },
  { date: "2024-04-25", primary: 215, secondary: 250 },
  { date: "2024-04-26", primary: 75, secondary: 130 },
  { date: "2024-04-27", primary: 383, secondary: 420 },
  { date: "2024-04-28", primary: 122, secondary: 180 },
  { date: "2024-04-29", primary: 315, secondary: 240 },
  { date: "2024-04-30", primary: 454, secondary: 380 },
  { date: "2024-05-01", primary: 165, secondary: 220 },
  { date: "2024-05-02", primary: 293, secondary: 310 },
  { date: "2024-05-03", primary: 247, secondary: 190 },
  { date: "2024-05-04", primary: 385, secondary: 420 },
  { date: "2024-05-05", primary: 481, secondary: 390 },
  { date: "2024-05-06", primary: 498, secondary: 520 },
  { date: "2024-05-07", primary: 388, secondary: 300 },
  { date: "2024-05-08", primary: 149, secondary: 210 },
  { date: "2024-05-09", primary: 227, secondary: 180 },
  { date: "2024-05-10", primary: 293, secondary: 330 },
  { date: "2024-05-11", primary: 335, secondary: 270 },
  { date: "2024-05-12", primary: 197, secondary: 240 },
  { date: "2024-05-13", primary: 197, secondary: 160 },
  { date: "2024-05-14", primary: 448, secondary: 490 },
  { date: "2024-05-15", primary: 473, secondary: 380 },
  { date: "2024-05-16", primary: 338, secondary: 400 },
  { date: "2024-05-17", primary: 499, secondary: 420 },
  { date: "2024-05-18", primary: 315, secondary: 350 },
  { date: "2024-05-19", primary: 235, secondary: 180 },
  { date: "2024-05-20", primary: 177, secondary: 230 },
  { date: "2024-05-21", primary: 82, secondary: 140 },
  { date: "2024-05-22", primary: 81, secondary: 120 },
  { date: "2024-05-23", primary: 252, secondary: 290 },
  { date: "2024-05-24", primary: 294, secondary: 220 },
  { date: "2024-05-25", primary: 201, secondary: 250 },
  { date: "2024-05-26", primary: 213, secondary: 170 },
  { date: "2024-05-27", primary: 420, secondary: 460 },
  { date: "2024-05-28", primary: 233, secondary: 190 },
  { date: "2024-05-29", primary: 78, secondary: 130 },
  { date: "2024-05-30", primary: 340, secondary: 280 },
  { date: "2024-05-31", primary: 178, secondary: 230 },
  { date: "2024-06-01", primary: 178, secondary: 200 },
  { date: "2024-06-02", primary: 470, secondary: 410 },
  { date: "2024-06-03", primary: 103, secondary: 160 },
  { date: "2024-06-04", primary: 439, secondary: 380 },
  { date: "2024-06-05", primary: 88, secondary: 140 },
  { date: "2024-06-06", primary: 294, secondary: 250 },
  { date: "2024-06-07", primary: 323, secondary: 370 },
  { date: "2024-06-08", primary: 385, secondary: 320 },
  { date: "2024-06-09", primary: 438, secondary: 480 },
  { date: "2024-06-10", primary: 155, secondary: 200 },
  { date: "2024-06-11", primary: 92, secondary: 150 },
  { date: "2024-06-12", primary: 492, secondary: 420 },
  { date: "2024-06-13", primary: 81, secondary: 130 },
  { date: "2024-06-14", primary: 426, secondary: 380 },
  { date: "2024-06-15", primary: 307, secondary: 350 },
  { date: "2024-06-16", primary: 371, secondary: 310 },
  { date: "2024-06-17", primary: 475, secondary: 520 },
  { date: "2024-06-18", primary: 107, secondary: 170 },
  { date: "2024-06-19", primary: 341, secondary: 290 },
  { date: "2024-06-20", primary: 408, secondary: 450 },
  { date: "2024-06-21", primary: 169, secondary: 210 },
  { date: "2024-06-22", primary: 317, secondary: 270 },
  { date: "2024-06-23", primary: 480, secondary: 530 },
  { date: "2024-06-24", primary: 132, secondary: 180 },
  { date: "2024-06-25", primary: 141, secondary: 190 },
  { date: "2024-06-26", primary: 434, secondary: 380 },
  { date: "2024-06-27", primary: 448, secondary: 490 },
  { date: "2024-06-28", primary: 149, secondary: 200 },
  { date: "2024-06-29", primary: 103, secondary: 160 },
  { date: "2024-06-30", primary: 446, secondary: 400 },
];

// Default chart configuration
const defaultChartConfig = {
  primary: {
    label: "Primary",
    color: "hsl(var(--primary))",
  },
  secondary: {
    label: "Secondary",
    color: "hsl(var(--muted-foreground))",
  },
};

export function ChartAreaInteractive({
  data = defaultChartData,
  config = defaultChartConfig,
  title = "Analytics Overview",
  description = "Data visualization for the last 3 months",
  primaryKey = "primary",
  secondaryKey = "secondary",
  dateKey = "date",
  height = "250px",
  showTimeRange = true,
  defaultTimeRange = "90d",
  isLoading = false,
}) {
  const isMobile = useIsMobile();
  const [timeRange, setTimeRange] = React.useState(defaultTimeRange);

  React.useEffect(() => {
    if (isMobile && defaultTimeRange === "90d") {
      setTimeRange("7d");
    }
  }, [isMobile, defaultTimeRange]);

  const filteredData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data.filter((item) => {
      const date = new Date(item[dateKey]);
      const referenceDate = new Date();
      let daysToSubtract = 90;
      if (timeRange === "30d") {
        daysToSubtract = 30;
      } else if (timeRange === "7d") {
        daysToSubtract = 7;
      }
      const startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - daysToSubtract);
      return date >= startDate;
    });
  }, [data, timeRange, dateKey]);

  if (isLoading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <div className="flex h-[250px] w-full items-center justify-center">
            <div className="text-muted-foreground">Loading chart data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">{description}</span>
          <span className="@[540px]/card:hidden">
            {description.length > 30
              ? description.substring(0, 30) + "..."
              : description}
          </span>
        </CardDescription>
        {showTimeRange && (
          <CardAction>
            <ToggleGroup
              type="single"
              value={timeRange}
              onValueChange={setTimeRange}
              variant="outline"
              className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
            >
              <ToggleGroupItem value="90d">Last 3 months</ToggleGroupItem>
              <ToggleGroupItem value="30d">Last 30 days</ToggleGroupItem>
              <ToggleGroupItem value="7d">Last 7 days</ToggleGroupItem>
            </ToggleGroup>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger
                className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
                size="sm"
                aria-label="Select a value"
              >
                <SelectValue placeholder="Last 3 months" />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                <SelectItem value="90d" className="rounded-lg">
                  Last 3 months
                </SelectItem>
                <SelectItem value="30d" className="rounded-lg">
                  Last 30 days
                </SelectItem>
                <SelectItem value="7d" className="rounded-lg">
                  Last 7 days
                </SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        )}
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={config}
          className={`aspect-auto w-full`}
          style={{ height }}
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillPrimary" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={`var(--color-${primaryKey})`}
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor={`var(--color-${primaryKey})`}
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillSecondary" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={`var(--color-${secondaryKey})`}
                  stopOpacity={0.6}
                />
                <stop
                  offset="95%"
                  stopColor={`var(--color-${secondaryKey})`}
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey={dateKey}
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : 10}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            {secondaryKey && (
              <Area
                dataKey={secondaryKey}
                type="natural"
                fill="url(#fillSecondary)"
                stroke={`var(--color-${secondaryKey})`}
                stackId="a"
              />
            )}
            <Area
              dataKey={primaryKey}
              type="natural"
              fill="url(#fillPrimary)"
              stroke={`var(--color-${primaryKey})`}
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
