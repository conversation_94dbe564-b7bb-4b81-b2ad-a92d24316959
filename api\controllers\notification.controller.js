import mongoose from "mongoose";
import Notification from "../models/notification.model.js";
import User from "../models/user.model.js";

export const createNotification = async (req, res) => {
  try {
    const notificationData = req.body;
    notificationData.createdBy = req.user.id;

    // Set schoolId based on user role
    if (req.user.role === "school-admin" || req.user.role === "teacher") {
      notificationData.schoolId = req.user.schoolId;
    }
    // For admin users, schoolId can be null
    if (notificationData.recipients !== "specific-class") {
      notificationData.specificClass = null;
    } else if (!notificationData.specificClass) {
      return res.status(400).json({
        success: false,
        message: "Class selection is required when sending to a specific class",
      });
    }

    const newNotification = new Notification(notificationData);
    await newNotification.save();

    if (newNotification.scheduleType === "immediate") {
      if (newNotification.sendEmail) {
        console.log("Email notification would be sent here");
      }

      // For SMS notifications
      if (newNotification.sendSMS) {
        console.log("SMS notification would be sent here");
      }
    }

    res.status(201).json({
      success: true,
      message: "Notification created successfully",
      data: newNotification,
    });
  } catch (error) {
    console.error("Create Notification Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all notifications
export const getAllNotifications = async (req, res) => {
  try {
    const { schoolId, type, status, priority } = req.query;

    let query = {};
    if (schoolId) {
      query.schoolId = schoolId;
    }
    if (type) {
      query.type = type;
    }
    if (status) {
      query.status = status;
    }
    if (priority) {
      query.priority = priority;
    }
    // For school-admin and teacher, filter by their school
    if (req.user.role === "school-admin" || req.user.role === "teacher") {
      query.schoolId = req.user.schoolId;
    }
    // For admin users, don't add schoolId filter (they can see all notifications)
    if (req.user.role === "student" || req.user.role === "parent") {
      query.$or = [
        { recipients: "all-users" },
        {
          recipients:
            req.user.role === "student" ? "all-students" : "all-parents",
        },
        { specificUsers: req.user.id },
      ];

      if (req.user.role === "student" && req.user.classId) {
        query.$or.push({
          recipients: "specific-class",
          specificClass: req.user.classId,
        });
      }
    }

    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .populate("createdBy", "name")
      .populate("schoolId", "name");

    res.status(200).json({
      success: true,
      count: notifications.length,
      data: notifications,
    });
  } catch (error) {
    console.error("Get All Notifications Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get notification by ID
export const getNotificationById = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    if (req.user.role !== "admin") {
      if (
        (req.user.role === "school-admin" || req.user.role === "teacher") &&
        notification.schoolId &&
        notification.schoolId._id.toString() !== req.user.schoolId.toString()
      ) {
        return res.status(403).json({
          success: false,
          message: "You don't have permission to view this notification",
        });
      }

      if (req.user.role === "student" || req.user.role === "parent") {
        const isRelevant =
          notification.recipients === "all-users" ||
          (req.user.role === "student" &&
            notification.recipients === "all-students") ||
          (req.user.role === "parent" &&
            notification.recipients === "all-parents") ||
          notification.specificUsers.some(
            (user) => user._id.toString() === req.user.id
          ) ||
          (req.user.role === "student" &&
            notification.recipients === "specific-class" &&
            req.user.classId &&
            notification.specificClass &&
            notification.specificClass._id.toString() ===
              req.user.classId.toString());

        if (!isRelevant) {
          return res.status(403).json({
            success: false,
            message: "You don't have permission to view this notification",
          });
        }
      }
    }

    // Mark notification as read for this user if not already read
    if (
      !notification.readBy.some(
        (read) => read.userId.toString() === req.user.id
      )
    ) {
      notification.readBy.push({
        userId: req.user.id,
        readAt: new Date(),
      });
      await notification.save();
    }

    res.status(200).json({
      success: true,
      data: notification,
    });
  } catch (error) {
    console.error("Get Notification By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update notification
export const updateNotification = async (req, res) => {
  try {
    const notificationData = req.body;

    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    // Check if user has permission to update this notification
    if (
      req.user.role !== "admin" &&
      notification.createdBy.toString() !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to update this notification",
      });
    }

    if (notification.status === "sent") {
      return res.status(400).json({
        success: false,
        message: "Cannot update a notification that has already been sent",
      });
    }

    // Handle specificClass field - if recipients is not specific-class, set to null
    if (notificationData.recipients !== "specific-class") {
      notificationData.specificClass = null;
    } else if (!notificationData.specificClass) {
      // If specific class is required but not provided
      return res.status(400).json({
        success: false,
        message: "Class selection is required when sending to a specific class",
      });
    }

    const updatedNotification = await Notification.findByIdAndUpdate(
      req.params.id,
      notificationData,
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      message: "Notification updated successfully",
      data: updatedNotification,
    });
  } catch (error) {
    console.error("Update Notification Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete notification
export const deleteNotification = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    if (
      req.user.role !== "admin" &&
      notification.createdBy.toString() !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to delete this notification",
      });
    }

    if (notification.status === "sent") {
      return res.status(400).json({
        success: false,
        message: "Cannot delete a notification that has already been sent",
      });
    }

    await Notification.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: "Notification deleted successfully",
    });
  } catch (error) {
    console.error("Delete Notification Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Send a notification immediately
export const sendNotification = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    if (
      req.user.role !== "admin" &&
      notification.createdBy.toString() !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: "You don't have permission to send this notification",
      });
    }

    if (notification.status === "sent") {
      return res.status(400).json({
        success: false,
        message: "Notification has already been sent",
      });
    }

    if (notification.sendEmail) {
      console.log("Email notification would be sent here");
    }

    if (notification.sendSMS) {
      console.log("SMS notification would be sent here");
    }

    notification.status = "sent";
    notification.sentAt = new Date();
    await notification.save();

    res.status(200).json({
      success: true,
      message: "Notification sent successfully",
      data: notification,
    });
  } catch (error) {
    console.error("Send Notification Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Mark notification as read
export const markAsRead = async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    // Check if notification is already read by this user
    if (
      notification.readBy.some((read) => read.userId.toString() === req.user.id)
    ) {
      return res.status(200).json({
        success: true,
        message: "Notification already marked as read",
        data: notification,
      });
    }

    // Mark notification as read
    notification.readBy.push({
      userId: req.user.id,
      readAt: new Date(),
    });
    await notification.save();

    res.status(200).json({
      success: true,
      message: "Notification marked as read",
      data: notification,
    });
  } catch (error) {
    console.error("Mark Notification as Read Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
