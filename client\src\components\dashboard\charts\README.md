# Reusable Chart Components

This directory contains reusable chart components for the dashboard overview pages.

## ChartAreaInteractive

The main reusable chart component that can be customized for different data types and use cases.

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | Array | `defaultChartData` | Array of data objects with date and metric values |
| `config` | Object | `defaultChartConfig` | Chart configuration for colors and labels |
| `title` | String | `"Analytics Overview"` | Chart title displayed in header |
| `description` | String | `"Data visualization for the last 3 months"` | Chart description |
| `primaryKey` | String | `"primary"` | Key for primary data series |
| `secondaryKey` | String | `"secondary"` | Key for secondary data series (optional) |
| `dateKey` | String | `"date"` | Key for date field in data |
| `height` | String | `"250px"` | Chart height |
| `showTimeRange` | Boolean | `true` | Show time range selector |
| `defaultTimeRange` | String | `"90d"` | Default time range ("7d", "30d", "90d") |
| `isLoading` | Boolean | `false` | Show loading state |

### Data Format

Data should be an array of objects with the following structure:

```javascript
[
  {
    date: "2024-01-01",
    primary: 100,
    secondary: 80
  },
  // ... more data points
]
```

### Config Format

Config object defines the appearance of data series:

```javascript
{
  primary: {
    label: "Primary Metric",
    color: "hsl(var(--primary))"
  },
  secondary: {
    label: "Secondary Metric", 
    color: "hsl(var(--muted-foreground))"
  }
}
```

### Basic Usage

```jsx
import { ChartAreaInteractive } from "@/components/dashboard/chart-area-interactive";

const data = [
  { date: "2024-01-01", users: 100, sessions: 150 },
  { date: "2024-01-02", users: 120, sessions: 180 },
  // ... more data
];

const config = {
  users: {
    label: "Users",
    color: "hsl(var(--primary))"
  },
  sessions: {
    label: "Sessions",
    color: "hsl(var(--muted-foreground))"
  }
};

<ChartAreaInteractive
  data={data}
  config={config}
  title="User Analytics"
  description="User and session data over time"
  primaryKey="users"
  secondaryKey="sessions"
  dateKey="date"
/>
```

## Pre-built Chart Components

### AdminActivityChart
- Displays system-wide growth metrics
- Shows schools and users over time
- Optimized for admin dashboard

### SchoolAdminActivityChart  
- Displays school-specific metrics
- Shows students and teachers over time
- Optimized for school admin dashboard

### NotificationActivityChart
- Displays notification metrics
- Shows sent and pending notifications
- Can be used in any dashboard

### Usage

```jsx
import { 
  AdminActivityChart, 
  SchoolAdminActivityChart,
  NotificationActivityChart 
} from "@/components/dashboard/charts";

// In your component
<AdminActivityChart isLoading={isLoading} />
<SchoolAdminActivityChart isLoading={isLoading} />
<NotificationActivityChart isLoading={isLoading} />
```

## Creating Custom Charts

To create a new chart component:

1. Create a new file in the `charts` directory
2. Import `ChartAreaInteractive`
3. Define your data transformation logic
4. Configure chart appearance
5. Export your component

Example:

```jsx
import React from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";

export function MyCustomChart({ isLoading = false }) {
  const data = generateMyData(); // Your data logic
  
  const config = {
    metric1: {
      label: "My Metric",
      color: "hsl(var(--primary))"
    }
  };

  return (
    <ChartAreaInteractive
      data={data}
      config={config}
      title="My Custom Chart"
      description="Custom chart description"
      primaryKey="metric1"
      isLoading={isLoading}
    />
  );
}
```

## Integration with Dashboard Context

Charts can integrate with the dashboard context for real-time data:

```jsx
import { useDashboard } from "@/context/dashboard-context";

export function RealTimeChart() {
  const { dashboardData, isLoading } = useDashboard();
  
  // Transform dashboard data for chart
  const chartData = transformData(dashboardData);
  
  return (
    <ChartAreaInteractive
      data={chartData}
      isLoading={isLoading}
      // ... other props
    />
  );
}
```
