import React, { useEffect, useState } from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";
import { useDashboard } from "@/context/dashboard-context";

/**
 * Example of how to use the reusable chart with real-time data from API
 * This component demonstrates fetching data and using it with the chart
 */
export function RealTimeChartExample() {
  const { dashboardData, isLoading } = useDashboard();
  const [chartData, setChartData] = useState([]);

  // Transform dashboard data into chart format
  useEffect(() => {
    if (dashboardData?.recentActivity) {
      // This is an example of how you might transform your API data
      // Replace this with your actual data transformation logic
      const transformedData = generateChartDataFromAPI(dashboardData);
      setChartData(transformedData);
    }
  }, [dashboardData]);

  // Example function to transform API data into chart format
  const generateChartDataFromAPI = (apiData) => {
    // This is sample transformation - replace with your actual logic
    const data = [];
    const currentDate = new Date();
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(currentDate);
      date.setDate(date.getDate() - i);
      
      // Use real data from API or generate based on API data
      data.push({
        date: date.toISOString().split('T')[0],
        schools: apiData.overview?.totalSchools?.current || 0,
        users: apiData.overview?.totalUsers?.current || 0,
        activity: apiData.overview?.systemActivity?.current || 0,
      });
    }
    
    return data;
  };

  const chartConfig = {
    schools: {
      label: "Schools",
      color: "hsl(var(--primary))",
    },
    users: {
      label: "Users", 
      color: "hsl(var(--muted-foreground))",
    },
    activity: {
      label: "System Activity",
      color: "hsl(var(--accent))",
    },
  };

  return (
    <ChartAreaInteractive
      data={chartData}
      config={chartConfig}
      title="Real-time System Analytics"
      description="Live data from your dashboard API"
      primaryKey="schools"
      secondaryKey="users"
      dateKey="date"
      height="350px"
      showTimeRange={true}
      defaultTimeRange="30d"
      isLoading={isLoading}
    />
  );
}

/**
 * Example of a simple single-metric chart
 */
export function SimpleMetricChart({ 
  data, 
  title = "Metric Overview", 
  metricKey = "value",
  isLoading = false 
}) {
  const chartConfig = {
    [metricKey]: {
      label: title,
      color: "hsl(var(--primary))",
    },
  };

  return (
    <ChartAreaInteractive
      data={data}
      config={chartConfig}
      title={title}
      description="Single metric visualization"
      primaryKey={metricKey}
      secondaryKey={null} // No secondary line
      dateKey="date"
      height="250px"
      showTimeRange={false} // Disable time range selector
      isLoading={isLoading}
    />
  );
}

/**
 * Example of a custom styled chart
 */
export function CustomStyledChart({ data, isLoading = false }) {
  const customConfig = {
    revenue: {
      label: "Revenue",
      color: "hsl(142, 76%, 36%)", // Green
    },
    expenses: {
      label: "Expenses",
      color: "hsl(0, 84%, 60%)", // Red
    },
  };

  return (
    <ChartAreaInteractive
      data={data}
      config={customConfig}
      title="Financial Overview"
      description="Revenue vs Expenses comparison"
      primaryKey="revenue"
      secondaryKey="expenses"
      dateKey="date"
      height="400px"
      showTimeRange={true}
      defaultTimeRange="90d"
      isLoading={isLoading}
    />
  );
}
