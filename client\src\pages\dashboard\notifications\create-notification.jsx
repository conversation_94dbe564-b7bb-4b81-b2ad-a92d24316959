import React, { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { NotificationForm } from "@/components/forms/dashboard/notifications/notification-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";

const CreateNotification = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Notification" : "Create New Notification"}
        actions={[
          { label: "Back to Notifications", href: "/dashboard/notifications" },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Notifications", href: "/dashboard/notifications" },
          { label: id ? "Edit Notification" : "Create Notification" },
        ]}
      />
      {loading ? (
        <FormCardSkeleton />
      ) : (
        <NotificationForm editingId={id} initialData={[]} />
      )}
    </Container>
  );
};

export default CreateNotification;
