import React from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";
import { useDashboard } from "@/context/dashboard-context";

export function AdminActivityChart() {
  const { dashboardData, isLoading } = useDashboard();

  // Get chart data from dashboard context
  const chartData = dashboardData?.chartData || [];

  return (
    <ChartAreaInteractive
      data={chartData}
      config={adminChartConfig}
      title="System Growth"
      description="Daily schools and users registration over time"
      primaryKey="schools"
      secondaryKey="users"
      dateKey="date"
      height="300px"
      showTimeRange={true}
      defaultTimeRange="90d"
      isLoading={isLoading}
    />
  );
}
