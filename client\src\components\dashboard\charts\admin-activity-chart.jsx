import React from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";

// Sample data for admin activity - this would come from your dashboard API
const generateAdminActivityData = () => {
  const data = [];
  const currentDate = new Date();
  
  for (let i = 89; i >= 0; i--) {
    const date = new Date(currentDate);
    date.setDate(date.getDate() - i);
    
    // Generate sample data - replace with real API data
    const schools = Math.floor(Math.random() * 10) + 15;
    const users = Math.floor(Math.random() * 50) + 100;
    
    data.push({
      date: date.toISOString().split('T')[0],
      schools: schools,
      users: users,
    });
  }
  
  return data;
};

const adminChartConfig = {
  schools: {
    label: "Schools",
    color: "hsl(var(--primary))",
  },
  users: {
    label: "Users",
    color: "hsl(var(--muted-foreground))",
  },
};

export function AdminActivityChart({ isLoading = false }) {
  const chartData = generateAdminActivityData();

  return (
    <ChartAreaInteractive
      data={chartData}
      config={adminChartConfig}
      title="System Growth"
      description="Schools and users growth over time"
      primaryKey="schools"
      secondaryKey="users"
      dateKey="date"
      height="300px"
      showTimeRange={true}
      defaultTimeRange="90d"
      isLoading={isLoading}
    />
  );
}
