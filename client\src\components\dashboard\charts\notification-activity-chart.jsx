import React from "react";
import { ChartAreaInteractive } from "../chart-area-interactive";

// Sample data for notification activity - this would come from your dashboard API
const generateNotificationActivityData = () => {
  const data = [];
  const currentDate = new Date();
  
  for (let i = 89; i >= 0; i--) {
    const date = new Date(currentDate);
    date.setDate(date.getDate() - i);
    
    // Generate sample data - replace with real API data
    const sent = Math.floor(Math.random() * 30) + 10;
    const pending = Math.floor(Math.random() * 10) + 2;
    
    data.push({
      date: date.toISOString().split('T')[0],
      sent: sent,
      pending: pending,
    });
  }
  
  return data;
};

const notificationChartConfig = {
  sent: {
    label: "Sent Notifications",
    color: "hsl(var(--primary))",
  },
  pending: {
    label: "Pending Notifications",
    color: "hsl(var(--muted-foreground))",
  },
};

export function NotificationActivityChart({ isLoading = false }) {
  const chartData = generateNotificationActivityData();

  return (
    <ChartAreaInteractive
      data={chartData}
      config={notificationChartConfig}
      title="Notification Activity"
      description="Sent and pending notifications over time"
      primaryKey="sent"
      secondaryKey="pending"
      dateKey="date"
      height="300px"
      showTimeRange={true}
      defaultTimeRange="90d"
      isLoading={isLoading}
    />
  );
}
